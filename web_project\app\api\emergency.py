from flask import Blueprint, request, jsonify, current_app
import uuid
from datetime import datetime
from app import db
from app.models.models import <PERSON><PERSON><PERSON>, ElderlyUser, FamilyUser, Smartwatch

# 创建蓝图，注意命名空间不要与URL前缀相同
emergency_bp = Blueprint('emergency_api', __name__)

@emergency_bp.route('/call', methods=['POST'])
def emergency_call():
    """
    触发紧急呼叫，记录老年人的紧急求助
    接收来自前端的请求数据: elderly_id, gps_location
    """
    data = request.json
    elderly_id = data.get('elderly_id')
    gps_location = data.get('gps_location')

    if not elderly_id:
        return jsonify({'error': '缺少老年人ID'}), 400

    # 检查老年人ID是否存在
    elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
    if not elderly:
        return jsonify({'error': '未找到该老年人用户'}), 404

    # 生成紧急呼叫ID (缩短ID长度，防止超出数据库限制)
    call_id = 'EC' + datetime.now().strftime('%m%d%H%M%S')

    # 如果没有提供GPS位置，尝试从智能手环获取
    if not gps_location and elderly.smartwatch_id:
        smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
        if smartwatch:
            gps_location = smartwatch.gps_location

    # 创建紧急呼叫记录
    emergency_call = EmergencyCall(
        call_id=call_id,
        elderly_id=elderly_id,
        gps_location=gps_location,
        status='待处理'
    )

    try:
        db.session.add(emergency_call)
        db.session.commit()

        # TODO: 在这里添加通知家属和社区工作人员的代码
        # notify_family_and_workers(elderly_id, call_id)

        return jsonify({
            'success': True,
            'message': '紧急呼叫已记录',
            'call_id': call_id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'保存紧急呼叫时出错: {str(e)}'}), 500

@emergency_bp.route('/calls/<elderly_id>', methods=['GET'])
def get_elderly_calls(elderly_id):
    """获取老年人的紧急呼叫历史记录"""
    try:
        calls = EmergencyCall.query.filter_by(elderly_id=elderly_id).order_by(EmergencyCall.call_time.desc()).all()

        result = []
        for call in calls:
            result.append({
                'call_id': call.call_id,
                'call_time': call.call_time.strftime('%Y-%m-%d %H:%M:%S'),
                'gps_location': call.gps_location,
                'status': call.status,
                'related_person_id': call.related_person_id
            })

        return jsonify({'calls': result}), 200
    except Exception as e:
        return jsonify({'error': f'获取紧急呼叫记录失败: {str(e)}'}), 500

@emergency_bp.route('/call/<call_id>/update', methods=['PUT'])
def update_call_status(call_id):
    """更新紧急呼叫的状态"""
    data = request.json
    new_status = data.get('status')
    related_person_id = data.get('related_person_id')

    if not new_status:
        return jsonify({'error': '缺少状态信息'}), 400

    try:
        call = EmergencyCall.query.filter_by(call_id=call_id).first()
        if not call:
            return jsonify({'error': '未找到该紧急呼叫记录'}), 404

        call.status = new_status
        if related_person_id:
            call.related_person_id = related_person_id

        db.session.commit()
        return jsonify({'success': True, 'message': '状态更新成功'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新状态失败: {str(e)}'}), 500

@emergency_bp.route('/location/<elderly_id>', methods=['GET'])
def get_elderly_location(elderly_id):
    """获取老年人当前位置"""
    try:
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        if not elderly.smartwatch_id:
            return jsonify({'error': '该用户未绑定智能手环'}), 400

        smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
        if not smartwatch:
            return jsonify({'error': '未找到该智能手环'}), 404

        return jsonify({
            'elderly_id': elderly_id,
            'name': elderly.name,
            'gps_location': smartwatch.gps_location,
            'last_sync_time': smartwatch.last_sync_time.strftime('%Y-%m-%d %H:%M:%S')
        }), 200
    except Exception as e:
        return jsonify({'error': f'获取位置信息失败: {str(e)}'}), 500

@emergency_bp.route('/elderly/locations', methods=['GET'])
def get_all_elderly_locations():
    """获取所有老年人的位置信息（用于社区工作人员地图显示）"""
    try:
        # 获取查询参数
        region = request.args.get('region', '')

        # 构建查询
        query = ElderlyUser.query
        if region:
            query = query.filter(ElderlyUser.region == region)

        elderly_users = query.all()

        locations = []
        for elderly in elderly_users:
            # 获取GPS位置信息
            gps_location = None
            if elderly.smartwatch_id:
                smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
                if smartwatch and smartwatch.gps_location:
                    gps_location = smartwatch.gps_location

            # 如果没有GPS位置，尝试从地址解析坐标
            if not gps_location and elderly.address:
                # 这里可以调用地理编码API将地址转换为坐标
                # 暂时使用模拟数据
                gps_location = "39.9042,116.4074"  # 默认坐标

            locations.append({
                'elderly_id': elderly.user_id,
                'name': elderly.name,
                'address': elderly.address,
                'gps_location': gps_location,
                'region': elderly.region,
                'phone': elderly.phone,
                'emergency_contact_name': elderly.emergency_contact_name,
                'emergency_contact_phone': elderly.emergency_contact_phone
            })

        return jsonify({'locations': locations}), 200

    except Exception as e:
        return jsonify({'error': f'获取位置信息失败: {str(e)}'}), 500